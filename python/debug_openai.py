#!/usr/bin/env python3
"""
Debug OpenAI API Connection
"""

import os
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

def test_openai_direct():
    """Test OpenAI API directly"""
    
    print("🔍 Debug OpenAI API Connection")
    print("=" * 35)
    
    # Get environment variables
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    print(f"API Key: {'✅ Set' if api_key else '❌ Missing'}")
    print(f"Base URL: {base_url if base_url else 'Default (api.openai.com)'}")
    
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return
    
    # Test different configurations
    configs = [
        {"name": "Default OpenAI", "api_key": api_key, "base_url": None},
        {"name": "Custom Base URL", "api_key": api_key, "base_url": base_url} if base_url else None
    ]
    
    configs = [c for c in configs if c is not None]
    
    for config in configs:
        print(f"\n🧪 Testing: {config['name']}")
        print("-" * 30)
        
        try:
            # Initialize client
            client_kwargs = {"api_key": config["api_key"]}
            if config["base_url"]:
                client_kwargs["base_url"] = config["base_url"]
                print(f"   Base URL: {config['base_url']}")
            
            client = OpenAI(**client_kwargs)
            
            # Test models list (if available)
            try:
                print("   📋 Testing models list...")
                models = client.models.list()
                print(f"   ✅ Found {len(models.data)} models")
                
                # Show embedding models
                embedding_models = [m for m in models.data if 'embedding' in m.id.lower()]
                if embedding_models:
                    print(f"   🎯 Embedding models: {[m.id for m in embedding_models[:3]]}")
                
            except Exception as e:
                print(f"   ⚠️ Models list failed: {e}")
            
            # Test embedding with different models
            test_models = [
                "text-embedding-3-small",
                "text-embedding-ada-002",
                "text-embedding-3-large"
            ]
            
            test_text = "Hello world"
            
            for model in test_models:
                try:
                    print(f"   🧪 Testing model: {model}")
                    
                    response = client.embeddings.create(
                        model=model,
                        input=test_text
                    )
                    
                    if response.data:
                        embedding = response.data[0].embedding
                        print(f"   ✅ {model}: {len(embedding)} dimensions")
                        break
                    else:
                        print(f"   ❌ {model}: No data returned")
                        
                except Exception as e:
                    print(f"   ❌ {model}: {e}")
                    
        except Exception as e:
            print(f"   ❌ Client initialization failed: {e}")

def test_openai_with_vietnamese():
    """Test OpenAI with Vietnamese text"""
    
    print(f"\n🇻🇳 Testing Vietnamese Text")
    print("=" * 30)
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    if not api_key:
        print("❌ API key not available")
        return
    
    try:
        client_kwargs = {"api_key": api_key}
        if base_url:
            client_kwargs["base_url"] = base_url
        
        client = OpenAI(**client_kwargs)
        
        vietnamese_texts = [
            "Học phí FPT bao nhiêu?",
            "Điểm chuẩn đại học 2024",
            "Thông tin tuyển sinh",
            "Campus facilities"
        ]
        
        for text in vietnamese_texts:
            try:
                print(f"🧪 Testing: '{text}'")
                
                response = client.embeddings.create(
                    model="text-embedding-3-small",
                    input=text
                )
                
                if response.data:
                    embedding = response.data[0].embedding
                    print(f"   ✅ Success: {len(embedding)} dimensions")
                    print(f"   📊 Sample: {embedding[:3]}")
                else:
                    print(f"   ❌ No data returned")
                    
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                
    except Exception as e:
        print(f"❌ Vietnamese test failed: {e}")

def test_request_format():
    """Test different request formats"""
    
    print(f"\n🔧 Testing Request Formats")
    print("=" * 30)
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    if not api_key:
        return
    
    try:
        client_kwargs = {"api_key": api_key}
        if base_url:
            client_kwargs["base_url"] = base_url
        
        client = OpenAI(**client_kwargs)
        
        # Test different input formats
        test_cases = [
            {"name": "String input", "input": "test"},
            {"name": "List input", "input": ["test"]},
            {"name": "Unicode text", "input": "Học phí 💰"},
            {"name": "Empty string", "input": ""},
            {"name": "Long text", "input": "This is a longer text to test if length matters for the embedding API call"}
        ]
        
        for case in test_cases:
            try:
                print(f"🧪 {case['name']}: {repr(case['input'])}")
                
                if case['input'] == "":
                    print("   ⚠️ Skipping empty string")
                    continue
                
                response = client.embeddings.create(
                    model="text-embedding-3-small",
                    input=case['input']
                )
                
                if response.data:
                    print(f"   ✅ Success")
                else:
                    print(f"   ❌ No data")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Request format test failed: {e}")

def main():
    """Main entry point"""
    try:
        test_openai_direct()
        test_openai_with_vietnamese()
        test_request_format()
        
        print(f"\n🎉 OpenAI Debug Complete!")
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
