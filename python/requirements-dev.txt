# Development dependencies for FPT University Agent - Refactored

# Include base requirements
-r requirements.txt

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-benchmark>=4.0.0

# Type checking
mypy>=1.5.0
types-redis>=4.6.0

# Code formatting and linting
black>=23.7.0
flake8>=6.0.0
isort>=5.12.0
bandit>=1.7.5

# Pre-commit hooks
pre-commit>=3.3.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.1.0
mkdocs-mermaid2-plugin>=1.1.0

# Performance profiling
py-spy>=0.3.14
memory-profiler>=0.61.0

# Optional full dependencies for development
qdrant-client>=1.6.0
openai>=1.0.0
sentence-transformers>=2.2.0
torch>=2.0.0
redis>=4.5.0
transformers>=4.30.0

# Jupyter for experimentation
jupyter>=1.0.0
ipykernel>=6.25.0
