#!/usr/bin/env python3
"""
Test Vector Search Components
"""

import sys
import asyncio
import time
from pathlib import Path

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from infrastructure.vector_stores.qdrant_store import QdrantVectorStore
from infrastructure.embeddings.openai_embeddings import OpenAIEmbeddingService
from shared.utils.metrics import MetricsCollectorImpl

async def test_vector_components():
    """Test vector search components individually"""
    
    print("🧪 Testing Vector Search Components")
    print("=" * 40)
    
    # Test 1: Qdrant connection
    print("\n1. Testing Qdrant connection...")
    try:
        vector_store = QdrantVectorStore()
        if vector_store.available:
            print("✅ Qdrant connection successful")
            
            # Get collection info
            collection_info = await vector_store.get_collection_info()
            points_count = collection_info.get("points_count", 0)
            print(f"📊 Collection points: {points_count}")
            
            if points_count == 0:
                print("⚠️ Collection is empty - vector search will not work")
            else:
                print(f"✅ Collection ready with {points_count} examples")
        else:
            print("❌ Qdrant connection failed")
            return
    except Exception as e:
        print(f"❌ Qdrant test failed: {e}")
        return
    
    # Test 2: OpenAI embeddings
    print("\n2. Testing OpenAI embeddings...")
    try:
        metrics_collector = MetricsCollectorImpl()
        embedding_service = OpenAIEmbeddingService(metrics_collector=metrics_collector)
        
        if embedding_service.available:
            print("✅ OpenAI embeddings service initialized")
            
            # Test actual embedding
            test_text = "Học phí FPT bao nhiêu?"
            print(f"🧪 Testing embedding for: '{test_text}'")
            
            start_time = time.time()
            embedding = await embedding_service.embed_text(test_text)
            duration = (time.time() - start_time) * 1000
            
            if embedding:
                print(f"✅ Embedding generated: {len(embedding)} dimensions")
                print(f"⚡ Duration: {duration:.1f}ms")
                print(f"📊 Sample values: {embedding[:5]}")
            else:
                print("❌ Failed to generate embedding")
                return
        else:
            print("❌ OpenAI embeddings service not available")
            return
    except Exception as e:
        print(f"❌ OpenAI embeddings test failed: {e}")
        return
    
    # Test 3: Vector search
    print("\n3. Testing vector search...")
    try:
        if points_count > 0:
            # Test search
            test_queries = [
                "Học phí FPT bao nhiêu?",
                "Tuition fee for AI program?",
                "Điểm chuẩn FPT 2024?"
            ]
            
            for query in test_queries:
                print(f"\n🔍 Searching: '{query}'")
                
                # Generate embedding
                query_embedding = await embedding_service.embed_text(query)
                if not query_embedding:
                    print("❌ Failed to generate query embedding")
                    continue
                
                # Search in vector store
                start_time = time.time()
                candidates = await vector_store.search(
                    query_vector=query_embedding,
                    top_k=3,
                    score_threshold=0.5
                )
                duration = (time.time() - start_time) * 1000
                
                print(f"⚡ Search duration: {duration:.1f}ms")
                print(f"📊 Found {len(candidates)} candidates")
                
                for i, candidate in enumerate(candidates, 1):
                    print(f"   {i}. Intent: {candidate.intent_id}")
                    print(f"      Score: {candidate.score:.3f}")
                    print(f"      Text: {candidate.text[:50]}...")
        else:
            print("⚠️ Skipping vector search test - collection is empty")
    except Exception as e:
        print(f"❌ Vector search test failed: {e}")
        return
    
    print("\n🎉 Vector search component tests completed!")

async def test_hybrid_with_vector():
    """Test hybrid detection with vector search enabled"""
    
    print("\n🔀 Testing Hybrid Detection with Vector Search")
    print("=" * 50)
    
    # Import hybrid components
    from core.domain.entities import DetectionContext
    from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
    from infrastructure.intent_detection.rule_loader import ProductionRuleLoader
    from infrastructure.caching.memory_cache import MemoryCacheService
    from core.application.services.hybrid_intent_service import HybridIntentDetectionService, HybridConfig
    from shared.utils.text_processing import VietnameseTextProcessor
    
    try:
        # Initialize components
        text_processor = VietnameseTextProcessor()
        metrics_collector = MetricsCollectorImpl()
        cache_service = MemoryCacheService(max_size=100, default_ttl=300)
        
        # Load rules
        loader = ProductionRuleLoader()
        rules = loader.load_rules()
        rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)
        
        # Initialize vector components
        vector_store = QdrantVectorStore()
        embedding_service = OpenAIEmbeddingService(metrics_collector=metrics_collector)
        
        if vector_store.available and embedding_service.available:
            print("✅ All components available")
            
            # Create hybrid service
            hybrid_config = HybridConfig(
                rule_high_confidence_threshold=0.7,
                rule_medium_confidence_threshold=0.3,
                vector_confidence_threshold=0.6,
                early_exit_threshold=0.8,
                vector_top_k=3
            )
            
            hybrid_service = HybridIntentDetectionService(
                rule_detector=rule_detector,
                vector_store=vector_store,
                embedding_service=embedding_service,
                cache_service=cache_service,
                text_processor=text_processor,
                metrics_collector=metrics_collector,
                config=hybrid_config
            )
            
            # Test queries
            test_queries = [
                "Học phí ngành AI bao nhiêu tiền?",
                "What is the tuition fee for software engineering?",
                "Điểm chuẩn vào FPT University 2024?",
                "Campus facilities at FPT?",
                "How to apply for scholarship?"
            ]
            
            print("\n🧪 Testing hybrid detection...")
            for i, query in enumerate(test_queries, 1):
                print(f"\n{i}. Query: '{query}'")
                
                context = DetectionContext(
                    query=query,
                    user_id="test_user",
                    language="vi" if any(ord(c) > 127 for c in query) else "en"
                )
                
                start_time = time.time()
                result = await hybrid_service.detect_intent(context)
                duration = (time.time() - start_time) * 1000
                
                confidence_icon = "🟢" if result.confidence >= 0.7 else "🟡" if result.confidence >= 0.5 else "🔴"
                print(f"   {confidence_icon} Intent: {result.id}")
                print(f"   📊 Confidence: {result.confidence:.3f}")
                print(f"   🔧 Method: {result.method}")
                print(f"   ⚡ Duration: {duration:.1f}ms")
                
                if result.metadata.get("vector_score"):
                    print(f"   🎯 Vector score: {result.metadata['vector_score']:.3f}")
        else:
            print("❌ Vector components not available for hybrid test")
            
    except Exception as e:
        print(f"❌ Hybrid test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main entry point"""
    try:
        asyncio.run(test_vector_components())
        asyncio.run(test_hybrid_with_vector())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
