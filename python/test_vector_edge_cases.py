#!/usr/bin/env python3
"""
Test Vector Search with Edge Cases and Difficult Queries
"""

import sys
import asyncio
import time
from pathlib import Path

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from core.domain.entities import DetectionContext
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.intent_detection.rule_loader import ProductionRuleLoader
from infrastructure.caching.memory_cache import MemoryCacheService
from infrastructure.vector_stores.qdrant_store import QdrantVectorStore
from infrastructure.embeddings.openai_embeddings import OpenAIEmbeddingService
from core.application.services.hybrid_intent_service import HybridIntentDetectionService, HybridConfig
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl
from shared.types import DetectionMethod

# Edge case test queries where vector search should help
EDGE_CASE_QUERIES = [
    # Queries with poor rule matching but good semantic meaning
    "How much does it cost to study at FPT?",  # Should be tuition_inquiry
    "What are the entry requirements?",        # Should be admission_requirements  
    "Tell me about the university campus",     # Should be campus_facilities
    "I need help with my student portal",      # Should be technical_support
    "What programs do you offer?",             # Should be program_information
    "Career opportunities after graduation",   # Should be graduation_career
    "Student support services available",      # Should be student_services
    "How can I contact the university?",       # Should be contact_information
    
    # Vietnamese queries with different phrasing
    "Chi phí học tập như thế nào?",           # Should be tuition_inquiry
    "Yêu cầu để vào trường?",                 # Should be admission_requirements
    "Cơ sở vật chất trường có gì?",           # Should be campus_facilities
    "Hỗ trợ sinh viên ra sao?",               # Should be student_services
    
    # Mixed language queries
    "FPT university tuition fee bao nhiêu?",  # Should be tuition_inquiry
    "Admission requirements cho international students?", # Should be admission_requirements
    
    # Ambiguous queries that need vector search
    "Tell me about FPT",                      # Could be general_information
    "What can you help me with?",             # Should be general_information
    "University information",                 # Could be general_information
]

async def test_edge_cases():
    """Test edge cases where vector search should provide better results"""
    
    print("🧪 Testing Edge Cases for Vector Search")
    print("=" * 45)
    
    # Initialize components
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    cache_service = MemoryCacheService(max_size=100, default_ttl=300)
    
    # Load rules
    loader = ProductionRuleLoader()
    rules = loader.load_rules()
    rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)
    
    # Initialize vector components
    vector_store = QdrantVectorStore()
    embedding_service = OpenAIEmbeddingService(metrics_collector=metrics_collector)
    
    if not (vector_store.available and embedding_service.available):
        print("❌ Vector components not available")
        return
    
    # Create hybrid service with lower thresholds to test vector search
    hybrid_config = HybridConfig(
        rule_high_confidence_threshold=0.7,
        rule_medium_confidence_threshold=0.3,
        vector_confidence_threshold=0.5,  # Lower to test more vector searches
        early_exit_threshold=0.9,         # Higher to force vector search
        vector_top_k=5
    )
    
    hybrid_service = HybridIntentDetectionService(
        rule_detector=rule_detector,
        vector_store=vector_store,
        embedding_service=embedding_service,
        cache_service=cache_service,
        text_processor=text_processor,
        metrics_collector=metrics_collector,
        config=hybrid_config
    )
    
    print("✅ Hybrid service initialized for edge case testing")
    print()
    
    # Test each edge case
    rule_only_count = 0
    vector_used_count = 0
    high_confidence_count = 0
    
    for i, query in enumerate(EDGE_CASE_QUERIES, 1):
        print(f"{i:2d}. Query: '{query}'")
        
        context = DetectionContext(
            query=query,
            user_id="edge_case_test",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )
        
        start_time = time.time()
        result = await hybrid_service.detect_intent(context)
        duration = (time.time() - start_time) * 1000
        
        # Analyze result
        confidence_icon = "🟢" if result.confidence >= 0.7 else "🟡" if result.confidence >= 0.5 else "🔴"
        method_icon = {
            DetectionMethod.RULE: "📏",
            DetectionMethod.VECTOR: "🔍", 
            DetectionMethod.HYBRID: "🔀",
            DetectionMethod.FALLBACK: "🔄"
        }.get(result.method, "❓")
        
        print(f"    {confidence_icon} Intent: {result.id}")
        print(f"    📊 Confidence: {result.confidence:.3f}")
        print(f"    {method_icon} Method: {result.method}")
        print(f"    ⚡ Duration: {duration:.1f}ms")
        
        # Show vector details if used
        if result.metadata.get("vector_score"):
            print(f"    🎯 Vector score: {result.metadata['vector_score']:.3f}")
            print(f"    📝 Source: {result.metadata.get('source_text', '')[:50]}...")
        
        # Count statistics
        if result.method == DetectionMethod.RULE:
            rule_only_count += 1
        elif result.method in [DetectionMethod.VECTOR, DetectionMethod.HYBRID]:
            vector_used_count += 1
        
        if result.confidence >= 0.7:
            high_confidence_count += 1
        
        print()
        await asyncio.sleep(0.1)  # Small delay to avoid rate limiting
    
    # Summary
    total_queries = len(EDGE_CASE_QUERIES)
    print("📊 Edge Case Test Summary:")
    print("-" * 30)
    print(f"Total queries: {total_queries}")
    print(f"Rule-only results: {rule_only_count}")
    print(f"Vector-enhanced results: {vector_used_count}")
    print(f"High confidence (≥0.7): {high_confidence_count}/{total_queries}")
    print(f"Vector usage rate: {vector_used_count/total_queries*100:.1f}%")
    
    # Performance stats
    if hybrid_service:
        stats = await hybrid_service.get_performance_stats()
        if "counters" in stats:
            counters = stats["counters"]
            print(f"\nPerformance:")
            print(f"Rule detections: {counters.get('rule_detections', 0)}")
            print(f"Vector searches: {counters.get('vector_searches', 0)}")
            print(f"Cache hits: {counters.get('cache_hits', 0)}")

async def test_vector_vs_rule_comparison():
    """Compare rule-only vs hybrid results"""
    
    print("\n🔀 Rule-Only vs Hybrid Comparison")
    print("=" * 35)
    
    # Initialize components
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    
    # Load rules
    loader = ProductionRuleLoader()
    rules = loader.load_rules()
    rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)
    
    # Initialize vector components
    vector_store = QdrantVectorStore()
    embedding_service = OpenAIEmbeddingService(metrics_collector=metrics_collector)
    
    if not (vector_store.available and embedding_service.available):
        print("❌ Vector components not available")
        return
    
    # Create hybrid service
    hybrid_config = HybridConfig(
        rule_high_confidence_threshold=0.7,
        rule_medium_confidence_threshold=0.3,
        vector_confidence_threshold=0.5,
        early_exit_threshold=0.8,
        vector_top_k=3
    )
    
    hybrid_service = HybridIntentDetectionService(
        rule_detector=rule_detector,
        vector_store=vector_store,
        embedding_service=embedding_service,
        cache_service=None,  # No cache for comparison
        text_processor=text_processor,
        metrics_collector=metrics_collector,
        config=hybrid_config
    )
    
    # Test queries where vector might help
    comparison_queries = [
        "How much does it cost to study here?",
        "What are the requirements to get in?",
        "Tell me about campus life",
        "I need technical assistance",
        "Career prospects after graduation"
    ]
    
    for query in comparison_queries:
        print(f"\nQuery: '{query}'")
        print("-" * 40)
        
        # Rule-only result
        rule_match = await rule_detector.detect(query)
        if rule_match:
            print(f"📏 Rule-only: {rule_match.intent_id} (score: {rule_match.score:.3f})")
        else:
            print("📏 Rule-only: No match")
        
        # Hybrid result
        context = DetectionContext(query=query, user_id="comparison_test")
        hybrid_result = await hybrid_service.detect_intent(context)
        print(f"🔀 Hybrid: {hybrid_result.id} (confidence: {hybrid_result.confidence:.3f}, method: {hybrid_result.method})")
        
        # Show if vector enhanced the result
        if hybrid_result.metadata.get("vector_score"):
            print(f"   🎯 Vector contributed with score: {hybrid_result.metadata['vector_score']:.3f}")

def main():
    """Main entry point"""
    try:
        asyncio.run(test_edge_cases())
        asyncio.run(test_vector_vs_rule_comparison())
        
        print("\n🎉 Edge case testing completed!")
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
