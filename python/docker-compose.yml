version: '3.8'

services:
  # Main application
  fpt-agent:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: fpt-agent
    environment:
      - FPT_AGENT_ENVIRONMENT=production
      - FPT_AGENT_LOG_LEVEL=INFO
      - FPT_AGENT_CACHE_BACKEND=redis
      - FPT_AGENT_VECTOR_BACKEND=qdrant
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
    depends_on:
      - redis
      - qdrant
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: fpt-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant vector database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: fpt-agent-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development service
  fpt-agent-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: fpt-agent-dev
    environment:
      - FPT_AGENT_ENVIRONMENT=development
      - FPT_AGENT_LOG_LEVEL=DEBUG
      - FPT_AGENT_CACHE_BACKEND=memory
      - FPT_AGENT_VECTOR_BACKEND=memory
    volumes:
      - .:/app
      - ./logs:/app/logs
    ports:
      - "8001:8000"
    command: python full_demo.py
    profiles:
      - dev

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: fpt-agent-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: fpt-agent-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  qdrant_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  default:
    name: fpt-agent-network
