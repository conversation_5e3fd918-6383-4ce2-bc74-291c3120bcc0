# FPT University Agent - Refactored Configuration
# Copy this file to .env and fill in your values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment: development, staging, production
FPT_AGENT_ENVIRONMENT=development

# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
FPT_AGENT_LOG_LEVEL=INFO

# Enable file logging
FPT_AGENT_ENABLE_FILE_LOGGING=true
FPT_AGENT_LOG_FILE=logs/fpt-agent.log

# =============================================================================
# INTENT DETECTION SETTINGS
# =============================================================================

# Enable/disable detection methods
FPT_AGENT_INTENT_ENABLE_CACHE=true
FPT_AGENT_INTENT_ENABLE_RULES=true
FPT_AGENT_INTENT_ENABLE_VECTOR=true
FPT_AGENT_INTENT_ENABLE_RERANKER=false

# Detection strategy
FPT_AGENT_INTENT_PARALLEL_EXECUTION=true
FPT_AGENT_INTENT_EARLY_EXIT_THRESHOLD=0.9
FPT_AGENT_INTENT_MAX_CANDIDATES=10

# Confidence thresholds
FPT_AGENT_INTENT_THRESHOLD_HIGH=0.7
FPT_AGENT_INTENT_THRESHOLD_MEDIUM=0.5
FPT_AGENT_INTENT_THRESHOLD_LOW=0.3

# =============================================================================
# VECTOR STORE SETTINGS
# =============================================================================

# Backend: memory, qdrant
FPT_AGENT_VECTOR_BACKEND=memory

# Qdrant settings (if using qdrant backend)
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=
QDRANT_COLLECTION_NAME=fpt_intents
QDRANT_VECTOR_DIMENSION=1536
QDRANT_DISTANCE_METRIC=Cosine

# Vector search settings
FPT_AGENT_VECTOR_TOP_K=5
FPT_AGENT_VECTOR_SIMILARITY_THRESHOLD=0.7

# =============================================================================
# EMBEDDING SETTINGS
# =============================================================================

# Provider: openai, local
FPT_AGENT_EMBEDDING_PROVIDER=local

# OpenAI settings (if using openai provider)
OPENAI_API_KEY=sk-your-openai-api-key-here
FPT_AGENT_OPENAI_MODEL=text-embedding-3-small
FPT_AGENT_OPENAI_DIMENSIONS=1536
FPT_AGENT_OPENAI_BATCH_SIZE=32
FPT_AGENT_OPENAI_MAX_RETRIES=3
FPT_AGENT_OPENAI_TIMEOUT=30.0

# Local embeddings settings (if using local provider)
FPT_AGENT_LOCAL_MODEL=sentence-transformers/all-MiniLM-L6-v2
FPT_AGENT_LOCAL_DEVICE=cpu
FPT_AGENT_LOCAL_BATCH_SIZE=32
FPT_AGENT_LOCAL_NORMALIZE=true

# =============================================================================
# CACHING SETTINGS
# =============================================================================

# Backend: memory, redis
FPT_AGENT_CACHE_BACKEND=memory

# Redis settings (if using redis backend)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=10

# Cache settings
FPT_AGENT_CACHE_DEFAULT_TTL=300
FPT_AGENT_CACHE_MAX_SIZE=1000
FPT_AGENT_CACHE_KEY_PREFIX=fpt_agent:

# =============================================================================
# RERANKER SETTINGS
# =============================================================================

# Reranker model
FPT_AGENT_RERANKER_MODEL=cross-encoder/ms-marco-MiniLM-L-6-v2
FPT_AGENT_RERANKER_DEVICE=cpu
FPT_AGENT_RERANKER_BATCH_SIZE=16
FPT_AGENT_RERANKER_SCORE_THRESHOLD=2.0

# =============================================================================
# TEXT PROCESSING SETTINGS
# =============================================================================

# Vietnamese text processing
FPT_AGENT_TEXT_ENABLE_NORMALIZATION=true
FPT_AGENT_TEXT_ENABLE_KEYWORD_EXTRACTION=true
FPT_AGENT_TEXT_MIN_KEYWORD_LENGTH=2
FPT_AGENT_TEXT_MAX_KEYWORDS=20

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Async settings
FPT_AGENT_MAX_CONCURRENCY=10
FPT_AGENT_REQUEST_TIMEOUT=30.0
FPT_AGENT_CONNECTION_POOL_SIZE=10

# Metrics collection
FPT_AGENT_ENABLE_METRICS=true
FPT_AGENT_METRICS_MAX_HISTORY=10000

# =============================================================================
# API SETTINGS (if running API server)
# =============================================================================

# Server settings
FPT_AGENT_API_HOST=0.0.0.0
FPT_AGENT_API_PORT=8000
FPT_AGENT_API_WORKERS=1

# CORS settings
FPT_AGENT_API_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
FPT_AGENT_API_CORS_METHODS=["GET", "POST"]

# Rate limiting
FPT_AGENT_API_RATE_LIMIT=100  # requests per minute
FPT_AGENT_API_RATE_LIMIT_BURST=10

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# API authentication
FPT_AGENT_API_KEY=your-api-key-here
FPT_AGENT_JWT_SECRET=your-jwt-secret-here
FPT_AGENT_JWT_EXPIRY=3600  # seconds

# Data privacy
FPT_AGENT_MASK_SENSITIVE_DATA=true
FPT_AGENT_LOG_QUERIES=false  # Set to false in production

# =============================================================================
# MONITORING SETTINGS
# =============================================================================

# Health check settings
FPT_AGENT_HEALTH_CHECK_INTERVAL=30  # seconds
FPT_AGENT_HEALTH_CHECK_TIMEOUT=10   # seconds

# Prometheus metrics (if enabled)
FPT_AGENT_PROMETHEUS_PORT=9090
FPT_AGENT_PROMETHEUS_PATH=/metrics

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Debug settings (development only)
FPT_AGENT_DEBUG=false
FPT_AGENT_RELOAD=false
FPT_AGENT_PROFILING=false

# Testing settings
FPT_AGENT_TEST_DATABASE_URL=sqlite:///test.db
FPT_AGENT_TEST_CACHE_BACKEND=memory
